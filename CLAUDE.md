# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

```bash
# Development
npm run dev                    # Start development server
npm run dev:concurrently      # Start dev server with Tailwind CSS watch

# Build for different environments  
npm run build:dev             # Build for development environment
npm run build:test            # Build for test environment
npm run build:prod            # Build for production environment
npm run build                 # Standard production build

# Code Quality
npm run lint                  # Run ESLint
npm run test:unit            # Run unit tests with Mocha

# Utilities
npm run tailwindcss          # Watch and compile Tailwind CSS
npm run analyzer             # Analyze bundle size
```

## Project Architecture

### Technology Stack
- **Framework**: Vue 2.6.11 with Vue CLI 4.5
- **UI Library**: Element UI 2.15.13 
- **State Management**: Vuex 3.4.0
- **Routing**: Vue Router 3.2.0
- **CSS Framework**: Tailwind CSS 3.3.5 + SCSS
- **Build Tool**: Webpack 4.46.0
- **Node Version**: 14.20.0 (configured via Volta)

### Key Directory Structure
```
src/
├── api/           # API service modules (business logic separated by domain)
├── assets/        # Static assets (images, CSS, fonts)
├── components/    # Reusable Vue components organized by feature
├── directive/     # Custom Vue directives
├── enum/          # Enumerations and constants
├── filters/       # Global Vue filters
├── minixs/        # Vue mixins for shared logic
├── router/        # Router configuration with modular structure
├── store/         # Vuex store modules
├── utils/         # Utility functions and helpers
└── views/         # Page components organized by feature domains
```

### Router Architecture
- **Dynamic Routing**: Routes are dynamically generated based on user permissions
- **Modular Structure**: Routes organized in `router/modules/` by business domain
- **Permission-Based**: Uses `router/routerPermission.js` for access control
- **Layout System**: Nested routing with shared layout component

### Component Organization
- **Domain-Driven**: Components grouped by business domain (e.g., `leadManagement/`, `invoiceManagement/`)
- **Reusable Components**: Prefixed with `xy-` for shared UI components
- **App Components**: Prefixed with `App` for application-specific components

### State Management Pattern
- **Modular Vuex**: Store split into domain-specific modules
- **Common Module**: Shared state in `store/modules/common.js`
- **User Module**: Authentication and user data management

### API Structure
- **Domain Separation**: API calls organized by business domain in `src/api/`
- **Axios Configuration**: Centralized HTTP client setup in `utils/axios.js`
- **Request/Response Handling**: Standardized error handling and request interceptors

### Styling Architecture
- **SCSS + Tailwind**: Hybrid approach using both SCSS and Tailwind CSS
- **Global Styles**: Shared styles in `assets/css/` with automatic SCSS imports
- **Element UI Theming**: Custom theme variables in `element-variables.scss`
- **Component Scoping**: Scoped styles within individual Vue components

### Environment Configuration
- **Multi-Environment**: Separate builds for dev, test, and production
- **Dynamic Environment Variables**: Custom build commands with `VUE_APP_` prefixed variables
- **Webpack Configuration**: Environment-specific optimizations in `vue.config.js`

## Development Guidelines

### Code Organization
- Follow the established modular pattern when adding new features
- API endpoints should be organized by business domain in `src/api/`
- New components should follow the existing naming conventions
- Use the established SCSS + Tailwind pattern for styling

### Testing
- Unit tests use Mocha framework
- Test files should follow the pattern: `tests/unit/**/*.spec.js`

### Building and Deployment
- The project supports multiple deployment environments via npm scripts
- Source maps are disabled in production for performance
- Webpack Bundle Analyzer available via `npm run analyzer`