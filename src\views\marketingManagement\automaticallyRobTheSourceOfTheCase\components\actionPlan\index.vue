<template>
	<app-drawer :size="700" title="自动抢案源计划配置" :visible.sync="getShow">
		<el-form :model="form" ref="form" class="!p-[24px] form-container" :rules="rules">
			<el-form-item label="设置抢案源时间范围" label-width="145px" prop="beginTime">
				<div class="flex flex-wrap">
					<el-time-picker
						class="!w-[360px]"
						is-range
						v-model="actionSourceTimeRange"
						value-format="HH:mm:ss"
						start-placeholder="开始时间"
						end-placeholder="结束时间"
						placeholder="选择时间范围"
						@change="handleTimeRangeChange"
					/>
					<div class="text-[12px] text-[#999999] leading-[1] pt-[12px]">
						仅可设置当日时间范围，不可与其他计划时间有交叉
					</div>
				</div>
			</el-form-item>

			<el-form-item label="抢案源上限" label-width="145px" prop="caseUpperLimit" class="pt-[16px]">
				<div class="flex flex-wrap">
					<el-input class="w-[360px]" v-model="form.caseUpperLimit" placeholder="请输入">
						<template #append>天</template>
					</el-input>
					<div class="text-[12px] text-[#999999] leading-[1] pt-[12px]">
						请谨慎配置改时段的上限，平台单日自动抢单总上限为<span class="text-[#3887F5]">{{
							systemParams.grabCaseUpperLimit
						}}</span
						>条
					</div>
				</div>
			</el-form-item>

			<el-form-item
				label="选择当事人所在地"
				label-width="145px"
				prop="regionCodeList"
				class="pt-[16px] is-required"
			>
				<div class="flex flex-col">
					<!-- 地区选择 -->
					<region
						ref="region"
						v-model="regionCodeList"
						clearable
						:get-names="true"
						:max-selection="systemParams.regionUpperLimit"
						@getregionsname="getRegionsName"
						multiple
					/>
					<div class="text-[12px] text-[#999999] leading-[1] pt-[12px]">
						最多可选{{ systemParams.regionUpperLimit }}项
					</div>
				</div>
			</el-form-item>

			<el-form-item prop="questTypeList" class="pt-[16px] case-types">
				<template #label>
					<div class="flex">
						选择案件类型偏好<span class="text-[12px] text-[#999999] pl-[10px]"
							>最多可选{{ systemParams.questTypeUpperLimit }}项</span
						>
					</div>
				</template>
				<case-types
					:is-need-internal-validation="false"
					ref="caseTypesRef"
					class="pt-[12px]"
					:max-selection="systemParams.questTypeUpperLimit"
					:is-show-title="false"
					:list="goodAtType"
				/>
			</el-form-item>

			<el-form-item prop="keyWord" class="pt-[16px] case-types">
				<template #label>
					<div class="flex">
						匹配关键词<span class="text-[12px] text-[#999999] pl-[10px]">最多可填3个</span>
					</div>
				</template>
				<div v-for="item in 3" :key="item">
					<div class="flex items-center pt-[12px]">
						<div class="text-[14px] flex-shrink-0 pr-[16px] text-[#333333]">关键词{{ item }}</div>
						<el-input
							type="text"
							maxlength="6"
							show-word-limit
							v-model="form.keyWord[item - 1]"
							placeholder="请输入"
						/>
					</div>
				</div>
			</el-form-item>
		</el-form>

		<template #footer>
			<div
				class="flex justify-end pt-[8px] pb-[24px] px-[24px] border-0 border-t-[1px] border-[#EEEEEE] border-solid"
			>
				<app-button class="mr-[16px]" type="info" @click="getShow = false">取消</app-button>
				<app-button delay-click type="primary" @click="submitForm">完成</app-button>
			</div>
		</template>
	</app-drawer>
</template>

<script>
import AppDrawer from "components/AppDrawer/index.vue";
import AppButton from "components/appButton/index.vue";
import Region from "components/regionCascader/index.vue";
import CaseTypes from "views/systemAdministration/caseSourcePush/components/caseTypes.vue";
import { commonConfigKey, dataDetailList } from "@/api/common";
import { autoGrabPlanDetail, autoGrabPlanInsert, autoGrabPlanUpdate } from "@/api/clues";

export default {
  name: "ActionPlan",
  components: { AppDrawer, AppButton, Region, CaseTypes },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      regionCodeList: [],
      // 案件类型列表
      goodAtType: [],
      form: {
        /* 设置抢案源时间范围 */
        beginTime: null,
        endTime: null,
        /* 抢案源上限 */
        caseUpperLimit: "",
        /* 选择当事人所在地 */
        regionCodeList: [],
        /* 选择案件类型偏好 */
        questTypeList: [],
        /* 匹配关键词 */
        keyWord: ["", "", ""],
      },
      /* 规则 */
      rules: {
        beginTime: [{ required: true, message: "请选择抢案源时间范围", trigger: "blur" }],
        caseUpperLimit: [{ required: true, message: "请输入抢案源上限", trigger: "blur" }],
        regionCodeList: [{ required: true, message: "请选择当事人所在地", trigger: "blur" }],
        questTypeList: [{ required: true, message: "请选择案件类型偏好", trigger: "blur" }],
        keyWord: [
          {
            validator: this.validateKeywords,
            trigger: "blur",
          },
        ],
      },
      /* 临时时间范围字段 */
      actionSourceTimeRange: null,
      /** 从系统参数获取到的各种上限 */
      systemParams: {},
    };
  },
  computed: {
    getShow: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    "editData.planId": {
      handler(val) {
        if (val) {
          this.setEditData();
        }
      },
      immediate: true,
    },
  },
  created() {
    this.getGoodAtType();
    this.getCommonConfigKey();
  },
  methods: {
    /**
		 * 如果是修改，则需要设置表单的默认值
		 */
    async setEditData() {
      try {
        const data = await autoGrabPlanDetail({
          planId: this.editData.planId,
        });

        // 设置时间范围
        this.form.beginTime = data.beginTime;
        this.form.endTime = data.endTime;
        this.actionSourceTimeRange = [data.beginTime, data.endTime];

        // 设置抢案源上限
        this.form.caseUpperLimit = data.caseUpperLimit;

        this.$refs.region.isBackShow = false;
        // 设置地区 - 转换为地区组件需要的格式
        this.regionCodeList = await this.convertRegionCodesToCascaderFormat(data.regionCodeList);
        this.$refs.region.isBackShow = true;

        // 需要在下次tick时设置案件类型组件的值
        this.$nextTick(() => {
          if (this.$refs.caseTypesRef) {
            this.$refs.caseTypesRef.checkList = data.questTypeIdList.map(String);
          }
        });

        // 设置关键词 - 解析JSON字符串
        try {
          const keyWordArray = JSON.parse(data.keyWord);
          this.form.keyWord =
						keyWordArray.length >= 3
						  ? keyWordArray.slice(0, 3)
						  : [...keyWordArray, ...Array(3 - keyWordArray.length).fill("")];
        } catch (e) {
          console.error("关键词解析失败:", e);
          this.form.keyWord = ["", "", ""];
        }
      } catch (error) {
        console.error("获取计划详情失败:", error);
        this.$message.error("获取计划详情失败");
      }
    },
    /**
     * 将地区代码数组转换为级联选择器需要的格式
     * @param {Array} regionCodes - 地区代码数组，如 [110100, 320100]
     * @returns {Array} 级联格式数组，如 [[110000, 110100], [320000, 320100]]
     */
    async convertRegionCodesToCascaderFormat(regionCodes) {
      if (!regionCodes || !Array.isArray(regionCodes)) {
        return [];
      }

      const result = [];

      for (const regionCode of regionCodes) {
        try {
          // 根据地区代码推断省代码
          const provinceCode = this.getProvinceCodeFromRegionCode(regionCode);

          if (provinceCode) {
            result.push([provinceCode, regionCode]);
          } else {
            // 如果无法推断省代码，尝试通过API查询
            console.warn(`无法推断地区代码 ${regionCode} 对应的省代码`);
            // 这里可以添加API调用来获取正确的省代码，暂时跳过
          }
        } catch (error) {
          console.error(`处理地区代码 ${regionCode} 时出错:`, error);
        }
      }

      return result;
    },
    /**
     * 根据地区代码推断省代码
     * 中国行政区划代码规则：前2位是省代码，中间2位是市代码，后2位是区县代码
     * @param {number} regionCode - 地区代码
     * @returns {number|null} 省代码
     */
    getProvinceCodeFromRegionCode(regionCode) {
      if (!regionCode) return null;

      const codeStr = regionCode.toString();

      // 如果是6位代码，前2位是省代码
      if (codeStr.length === 6) {
        const provincePrefix = codeStr.substring(0, 2);
        return parseInt(provincePrefix + "0000");
      }

      // 如果是4位代码，可能是市级代码，前2位是省代码
      if (codeStr.length === 4) {
        const provincePrefix = codeStr.substring(0, 2);
        return parseInt(provincePrefix + "0000");
      }

      // 特殊处理直辖市（北京11、天津12、上海31、重庆50）
      const directMunicipalities = {
        "11": 110000, // 北京
        "12": 120000, // 天津
        "31": 310000, // 上海
        "50": 500000  // 重庆
      };

      const prefix = codeStr.substring(0, 2);
      if (directMunicipalities[prefix]) {
        return directMunicipalities[prefix];
      }

      return null;
    },
    /** 获取新增限制 */
    getCommonConfigKey() {
      commonConfigKey({
        paramName: "auto_grab_plan_param",
      }).then(data => {
        try {
          this.systemParams = JSON.parse(data);
        } catch (e) {
          console.error(e);
        }
      });
    },
    /**
		 * 验证关键词的自定义函数
		 * 只要填写了任意一个关键词即可通过验证
		 */
    validateKeywords(rule, value, callback) {
      // 检查是否有任意一个关键词被填写
      const hasAnyKeyword = value && value.some(keyword => keyword && keyword.trim() !== "");

      if (!hasAnyKeyword) {
        callback(new Error("请输入匹配关键词"));
      } else {
        callback();
      }
    },
    getRegionsName(val) {
      console.log(val);
      console.log(
        this.form.regionCodeList,
        this.regionCodeList,
        "this.form.regionCodeList,this.regionCodeList",
      );
    },
    /* 处理时间范围 */
    handleTimeRangeChange(val) {
      if (val && val.length === 2) {
        this.form.beginTime = val[0];
        this.form.endTime = val[1];
      } else {
        this.form.beginTime = null;
        this.form.endTime = null;
      }
    },
    getGoodAtType() {
      dataDetailList({
        groupCode: "LAWYER_SPECIALITY",
      }).then(res => {
        this.goodAtType = res;
      });
    },
    /* 提交表单 */
    submitForm() {
      // 案件类型组件需要单独调用内部的方法获取值
      const { caseTypes = [] } = this.$refs.caseTypesRef.caseSure() || {};
      this.form.questTypeList = caseTypes.map(item => item.typeValue);
      this.form.regionCodeList = this.regionCodeList.map(item => item[1]);
      console.log(this.regionCodeList, "this.regionCodeListthis.regionCodeList");
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitToApi();
        } else {
          console.log("表单验证失败");
        }
      });
    },
    /* 调用接口提交 */
    async submitToApi() {
      try {
        // 构建提交数据
        const submitData = {
          beginTime: this.form.beginTime,
          endTime: this.form.endTime,
          caseUpperLimit: parseInt(this.form.caseUpperLimit),
          keyWord: this.form.keyWord,
          regionCodeList: this.form.regionCodeList,
          questTypeList: this.form.questTypeList,
        };

        if (this.editData.planId) {
          await autoGrabPlanUpdate({
            planId: this.editData.planId,
            ...submitData,
          });
        } else {
          await autoGrabPlanInsert(submitData);
        }
        this.$message.success("保存成功");
        this.$emit("refresh-list");
        this.getShow = false;
      } catch (error) {
        console.error("提交失败:", error);
        this.$message.error("保存失败");
      }
    },
  },
};
</script>

<style scoped lang="scss">
.form-container {
	::v-deep .el-form-item__label {
		padding-right: 8px;
		font-weight: 400;
		font-size: 14px;
		color: #333333;
	}
	::v-deep .el-form-item {
		margin-bottom: 0;
	}
	::v-deep .el-cascader {
		width: 360px;
	}
	.case-types {
		display: flex;
		flex-direction: column;
		::v-deep .case-lists-box {
			margin: 0;
		}
		::v-deep .el-form-item__label {
			display: flex;
		}
	}
}
</style>
