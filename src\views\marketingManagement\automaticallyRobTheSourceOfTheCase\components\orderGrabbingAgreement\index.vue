<template>
   <app-popup
      title="自动抢案源风险须知(试运行)"
      width="560px"
      class="order-grabbing-agreement"
      :show-title="false"
      :show-confirm="false"
      :visible.sync="showVisible"
    >
      <template>
        <div class="flex items-center justify-between border-0 border-b-[1px] border-solid border-[#EEEEEE] ">
          <div class="flex flex-1 items-center justify-center leading-[54px] font-[500] text-[16px] text-[#333333]">
            自动抢案源风险须知(试运行)
          </div>
          <i v-if="agreementConsentStatus" @click="close" class="iconfont pr-[24px] icon-xianxingtubiao-13 cursor-pointer" />
        </div>
      </template>
      <div class="box py-[16px] box-border h-[312px]">
        <iframe  ref="iframe" width="100%" height="100%" :src="url" />
      </div>
      <template #footerBtn>
        <div>
          <app-button :disabled="agreementConsentStatus" delay-click  @click="handleRootPopupClick">{{ btnText }}</app-button>
        </div>
      </template>
    </app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import { agreementLinks } from "utils/config";
import AppButton from "components/appButton/index.vue";


export default {
  name: "OrderGrabbingAgreement",
  components: { AppPopup, AppButton },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data(){
    return {
      /* 协议同意状态 */
      agreementConsentStatus: false
    };
  },
  computed: {
    showVisible: {
      get(){
        return this.show;
      },
      set(val){
        this.$emit("update:show", val);
      }
    },
    url(){
      /* 测试环境和开发环境使用*/
      return agreementLinks.automaticRobberySourceRiskProtocol;
    },
    btnText(){
      return this.agreementConsentStatus ? "我已知晓并同意" : "已知晓并同意";
    }
  },
  methods: {
    handleRootPopupClick(){
      this.close();
    },
    close(){
      this.$emit("update:show", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.order-grabbing-agreement{
  ::v-deep .app-popup_footer{
    justify-content: center;
  }
}
.box{
  ::v-deep iframe{
    border: none;
  }
}
</style>
