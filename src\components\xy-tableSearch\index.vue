<!-- 表格顶部搜索区域 -->
<template>
	<div :class="[`xy-table-search`]">
		<transition name="fade">
			<div v-if="!isCollapsed" class="content">
				<el-form inline :model="searchData" class="table-search-form">
					<!--   根据表格column中的search生成的筛选条件     -->
					<template v-if="searchForm.length">
						<div v-for="(item, index) in searchForm" :key="index">
							<el-form-item
								v-if="
									(item.controlItem &&
										item.controlItem['value'].includes(
											String(searchData[item.controlItem['name']]),
										)) ||
									!item.controlItem
								"
								:label="item.label"
								:label-width="item.labelWidth"
								:prop="item.prop"
							>
								<!--  单行文本输入框  -->
								<template v-if="item.type === 'input'">
									<el-input
										v-if="item.itemType === 'number'"
										v-model.trim.number="searchData[item.prop]"
										:placeholder="item.placeholder || item.label"
										:type="item.itemType"
										clearable
									/>
									<el-input
										v-else
										v-model.trim="searchData[item.prop]"
										:placeholder="item.placeholder || item.label"
										:type="item.itemType"
                    :maxlength="item.maxlength"
										clearable
									/>
								</template>
								<!--  日期段选择  -->
								<el-date-picker
									v-if="
										item.type === 'daterange' ||
										item.type === 'datetimerange' ||
										item.type === 'monthrange' ||
										item.type === 'date'
									"
									v-model="searchData[item.prop]"
									:picker-options="item.pickerDate || $global.threePickerOptions"
									:placeholder="item.placeholder || item.label"
									:type="item.type"
									:value-format="item.valueFormat || 'yyyy-MM-dd'"
									align="center"
									clearable
									prefix-icon="el-icon-date"
									end-placeholder="结束时间"
									start-placeholder="开始时间"
									@change="changeDateRange(item, searchData[item.prop])"
								/>

								<!--  下拉选择：单选  -->
								<el-select
									v-if="item.type === 'select'"
									v-model="searchData[item.prop]"
									:placeholder="item.placeholder || item.label"
									clearable
									filterable
									:multiple="item.multiple"
									@change="handleChange(item, $event)"
									class="el-select-tags"
								>
									<el-option
										v-for="(inner, i) in item.syncData.data"
										:key="i"
										:label="
											!item.isSpliceData
												? inner[item.syncData.label || 'label']
												: `${inner[item.syncData.label || 'label']} / ${
														inner[item.syncData.value || 'value']
												  }`
										"
										:value="inner[item.syncData.value || 'value']"
									/>
								</el-select>
								<!-- 地区选择 -->
								<region
									v-if="item.type === 'regioncascader'"
									v-model="searchData[item.prop]"
									:check-strictly="item['checkStrictly']"
									:clearable="true"
									:get-names="true"
                  collapse-tags
									@getregionsname="getRegionsName"
									:multiple="item.multiple"
								/>
							</el-form-item>
						</div>
					</template>

					<!--  自定义插入的筛选条件    -->
					<slot :search-data="searchData" name="searchContainer" />
					<div class="btn-wrapper">
						<el-form-item>
							<!--  查询按钮   -->
							<xySearchButton v-if="showSearch" @click="handleSearch" />
							<!--  重置按钮   -->
							<xyResetButton v-if="showReset" @click="handleReset" />
						</el-form-item>
						<!-- 其他按钮 -->
						<slot :search-data="searchData" name="searchBtn" />
					</div>
				</el-form>
			</div>
		</transition>
	</div>
</template>

<script>

import xySearchButton from "@/components/xy-buttons/searchButton.vue";
import xyResetButton from "@/components/xy-buttons/resetButton";
import Region from "@/components/regionCascader/index.vue";
import moment from "moment";

export default {
  name: "XyTableSearch",
  components: {
    xySearchButton,
    xyResetButton,
    Region,
  },
  props: {
    /* searchForm属性值说明:
		 * label: 表单label值
		 * labelWidth： 表单label宽度
		 * prop: 表单值
		 * type: 表单控件类型
		 * placeholder: 控件placeholder
		 * syncData: 下拉控件的值（数组），可以是静态数据也可以是接口返回
		 * syncFormat: 下拉控件绑定的字段值
		 * isSpliceData: 下拉框数据拼接 value + label
		 * dateFormat: 日期格式（日期控件使用） */
    searchForm: {
      type: Array,
      default: () => [],
      desc: "根据表格属性column中的search生成的筛选条件",
    },
    labelWidth: {
      type: String,
      default: "90px",
    },
    title: {
      type: String,
      default: "筛选条件",
    },
    showCollapsed: {
      type: Boolean,
      default: false,
      desc: "是否显示顶部的折叠操作图标",
    },
    showSearch: {
      type: Boolean,
      default: true,
      desc: "是否显示筛选条件后的查询按钮，否则显示顶部的查询按钮",
    },
    showReset: {
      type: Boolean,
      default: true,
      desc: "是否显示筛选条件后的重置按钮，否则显示顶部的重置按钮",
    },
    isHandlerRest: {
      type: [Boolean, String],
      default: false,
    },
    showHead: {
      type: Boolean,
      default: true,
      desc: "是否显示筛选条件头部",
    },
    background: {
      type: Boolean,
      default: true,
    },
    defindQuerySearch: {
      type: Object,
      default: () => {},
    },
    searchQuery: {
      type: Object,
      default: () => {
        return {};
      },
      desc: "query参数",
    },
    defaultLoadingPage: {
      type: Boolean,
      default: true,
      desc: "是否默认加载页面"
    }
  },
  data() {
    return {
      // 筛选条件
      searchData: {
        currentPage: 1,
      },
      isCollapsed: false,
      isRemoveField: [],
    };
  },
  computed: {
    ruleForm(val) {
      this.searchForm.map(res => {
        const obj = {};
        if (res.requireRule) {
          obj[res.prop] = res.requireRule;
          val = Object.assign({}, val, obj);
        }
      });
      return val;
    },
  },
  created() {
    this.getDefaultValue();
    // 是否默认加载页面
    if (this.defaultLoadingPage) {
      this.handleSearch();
    }else{
      this.getCacheSearch();
    }
  },

  methods: {
    handleChange(item) {
      if (item.resetKey) {
        (item.resetKey || []).forEach(key => {
          this.$set(this.searchData, key, "");
        });
      }
      if (item.multiple) {
        // 多选筛选
        this.$set(this.searchData, item.prop, this.searchData[item.prop]);
      }
    },
    // 缓存每一个路由的搜索条件
    handleCacheSearch() {
      const cacheKey = `xy-table-search:${this.$route.fullPath}`;
      localStorage.setItem(cacheKey, JSON.stringify({ ...this.searchData, ...this.searchQuery }));
    },
    // 获取缓存的搜索条件,且赋值
    getCacheSearch() {
      const cacheKey = `xy-table-search:${this.$route.fullPath}`;
      const cacheData = localStorage.getItem(cacheKey);
      if (cacheData) {
        this.searchData = JSON.parse(cacheData);
      } else {
        this.restValue();
      }
    },

    // 默认值
    getDefaultValue(val) {
      this.restValue();
      this.searchForm.map(item => {
        if (["daterange", "datetimerange"].includes(item.type)) {
          if (item.defaultValue) {
            const _val = item.defaultValue.map(r => {
              return   moment(r).format(item.valueFormat.replaceAll("dd", "DD"));
            });
            this.$set(this.searchData, item.prop, _val);
            this.changeDateRange(item, _val);
          } else {
            const _time = [
              this.searchQuery[item.propKey[0]] || "",
              this.searchQuery[item.propKey[1]] || "",
            ];
            this.$set(this.searchData, item.prop, _time);
            this.setValue(item.propKey, _time);
          }
        }else{
          if(item.defaultColumnValue){
            this.$set(this.searchData, item.prop, item.defaultColumnValue);
          }
        }
      });
      if (val === "rest") {
        console.log("重置搜索条件", this.searchData);

        // 清除缓存
        this.handleCacheSearch();
        this.$emit("handleSearchRest", this.searchData);
      }
    },
    // 新增按钮
    handleAdd() {
      this.$emit("handleAdd", true);
    },
    changeCollapsed() {
      this.isCollapsed = !this.isCollapsed;
    },
    restValue() {
      this.searchData = { currentPage: 1, pageSize: 10 };
    },
    // 条件搜索
    handleSearch() {
      // 搜索的时候自定义的参数
      this.searchData = Object.assign({}, this.searchData, this.defindQuerySearch);
      if(!this.defaultLoadingPage) this.handleCacheSearch();
      this.$emit("handleSearch", this.searchData);
    },
    // 重置
    handleReset() {
      this.getDefaultValue("rest");
    },
    // 日期筛选
    changeDateRange(item, value) {
      //   console.log(item, value)
      if (item.propKey && !this.basics.isArrNull(item.propKey)) {
        if (value) {
          this.setValue(item.propKey, this.basics.isNumber(value) ? [value] : value);
        } else {
          this.setValue(item.propKey);
        }
      }
    },
    // 地区选择
    getRegionsName(item, multiple) {
      if (!item) {
        this.setValue(["provinceCode", "regionCode", "provinceName", "cityName"]);
        return;
      }
      const { value, name } = JSON.parse(item);

      if (multiple) {
        // 多选筛选
        const arr = JSON.parse(item).map(i => i.value);
        this.searchData.provinceCodeList = arr.map(cI => cI[0]);
        this.searchData.regionCodeList = arr.map(cI => cI[1]);
        return true;
      }
      this.setValue(["provinceCode", "regionCode"], value);
      this.setValue(["provinceName", "cityName"], name);
    },
    // searchData赋值
    setValue(filed, value = [""]) {
      filed.map((res, i) => {
        this.searchData[res] = value[i] || "";
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.xy-table-search {
	&.border {
		border: 1px solid $weak-color;
	}
	.content {
		background: #fff;
	}
}
.el-select-tags /deep/ .el-select__tags {
	white-space: nowrap;
	overflow: hidden;
	flex-wrap: nowrap;
	text-overflow: ellipsis;
}
.el-select-tags /deep/ .el-select__tags-text {
	word-break: keep-all;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}
.table-search-form {
	//padding-bottom: 6px;
	::v-deep {
		.el-input .el-input__inner {
			width: 160px;
		}
    .region{
      .el-input .el-input__inner {
        width: 180px;
      }
    }
		.el-form-item {
			margin-right: 40px !important;
			margin-bottom: 16px;
			.el-form-item__label {
				font-size: 14px;
				font-weight: 400;
				color: #333333;
				padding-right: 8px;
			}
		}
		.btn-wrapper .el-form-item {
			margin-right: 8px !important;
			.el-button {
				border-radius: 4px;
			}
		}
		.app-button {
			margin-right: 8px;
			&:last-child {
				margin-right: 0;
			}
		}
		// 去除 number 类型的默认箭头样式
		input::-webkit-outer-spin-button,
		input::-webkit-inner-spin-button {
			-webkit-appearance: none;
		}
		input[type='number'] {
			-moz-appearance: textfield;
		}
	}
}
</style>
