<template>
  <div class="region">
    <el-cascader
        :value="value"
        :props="props"
        v-if="isBackShow"
        :clearable='clearable'
        @change='handleChange'
        :collapse-tags="collapseTags"
        :options="optionsLists"
        ref='regions' />
  </div>
</template>

<script>

import { areaGetArea } from "@/api/common";

export default {
  name: "Region",
  props: {
    collapseTags: {
      type: Boolean,
      default: false,
      desc: "多选时是否将选中值按文字的形式展示"
    },
    value: {
      type: Array,
      default: () => [],
      desc: "获取值"
    },
    checkStrictly: {
      type: Boolean,
      default: false,
      desc: "任意选择某一节点"
    },
    multiple: {
      type: Boolean,
      default: false,
      desc: "是否多选"
    },
    clearable: {
      type: Boolean,
      default: false,
      desc: "可清空"
    },
    level: {
      type: [Number, String],
      default: 1,
      desc: "选择到层级"
    },
    getNames: {
      type: Boolean,
      default: false,
      desc: "是否需要名字"
    },
    entireCountry: {
      type: Boolean,
      default: false,
      desc: "添加全国选项"
    },
    api: {
      type: [Function],
      default: areaGetArea,
      desc: "获取数据的接口"
    },
    maxSelection: {
      type: Number,
      default: 0,
      desc: "最多选择的数量"
    }
  },
  data() {
    return {
      props: {
        lazy: true,
        lazyLoad: this.lazyLoad,
        checkStrictly: this.checkStrictly,
        multiple: this.multiple
      },
      isBackShow: true,
      region: [0, 0],
      labels: [],
      optionsLists: [],
      disableValue: ""
    };
  },

  watch: {
    value: {
      handler(newValue, oldValue) {
        if(this.maxSelection > 0 && newValue.length > this.maxSelection){
          this.$message.error(`最多选择${this.maxSelection}个`);
          this.$nextTick(() => {
            this.handleChange(oldValue);
          });
          return;
        }
        this.region = newValue;
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 获取节点
    async lazyLoad(node, resolve) {
      const { level, value } = node; // 层级
      // 临时解决方案，排除重复的直辖市
      const el = node.children && node.children.find((el) => el.value === value);
      if (el) {
        this.$nextTick(() => {
          resolve();
        });
        return;
      }
      // 。。。

      // 全国直接返回 []
      if (value === 0) {
        resolve([]);
        return;
      }

      if (level === 0) {
        if (this.region.length > 0 && this.multiple) {
          const res = await this.formats();
          console.log(res, "res");
          this.optionsLists = res;
          resolve([]);
        } else {
          const lists = await this.getArea(level, value);
          // console.log(lists)
          resolve(lists);
        }
      } else {

        const lists = await this.getArea(level, value);

        // 去除已经存在的数据
        lists.forEach((r, i) => {
          if (node.children.find(e => e.value === r.value)) {
            delete lists[i];
          }
        });

        resolve(lists);
      }
    },

    /* 获取城市 */
    async getArea(level = 0, value = null) {
      const _data = {
        type: level + 1,
        code: value
      };
      let lists = await this.api(_data) || [];
      lists = lists.map(item => {
        return {
          value: item.code,
          label: item.name,
          leaf: level >= this.level,
          seletStatus: item.seletStatus,
          /* 不能选省就禁止*/
          disabled: item.seletStatus === 0 && level === 0 && this.multiple
        };
      });

      // 添加一个全国的选项，约定 value 为 0
      if (this.entireCountry && level === 0) {
        lists.unshift({
          value: 0,
          label: "全国",
          leaf: true }
        );
      }
      return lists;
    },

    /* 处理数据 */
    async formats() {
      const allList = await this.getArea();
      for (const region of this.region) {
        if (region.length > 1) {
          const find =  this.findItem(allList, region[0]);
          if((this.basics.isNull(find.children) || this.basics.isArrNull(find.children))){
            find.children = await this.getArea(1, region[0]);
          }
        }
      }
      return allList;
    },
    findItem(res, value) {
      for (let i = 0; i < res.length; i++) {
        if (res[i].value === value) {
          return res[i];
        }
        if (res[i].children) {
          this.findItem(res[i].children, value);
        }
      }
    },

    // lazyLoad(node, resolve) {
    //   // const isImplement = true
    //   const { level, value } = node // 层级

    //   // 临时解决方案，排除重复的直辖市
    //   const el = node.children && node.children.find((el) => el.value === value)
    //   if (el) {
    //     this.$nextTick(() => {
    //       resolve()
    //     })
    //     return
    //   }
    //   // 。。。

    //   const _data = {
    //     type: level + 1,
    //     code: value
    //   }

    //   console.log(level, value, node)

    //   getAreaList(_data).then(res => {
    //     const nodes = res.map(item => ({
    //       value: item.code,
    //       label: item.name,
    //       leaf: level >= this.level
    //     }))
    //     console.log(nodes)
    //     resolve(nodes)
    //   })
    // },
    // 选中的节点
    handleChange(value) {
      // 有全国选项才展示
      if (this.entireCountry) {
        if (this.isEntireCountry(value) && !this.isEntireCountry(this.value)) {
          value = [[0]];
        } else {
          value = value.reduce((arr, val) => {
            arr.push(val.filter((el) => el !== 0));
            return arr;
          }, []);
        }
      }
      // this.region = value
      this.$emit("input", value);
      // 获取地区的名字
      if (this.getNames) {
        const labels = this.$refs["regions"].getCheckedNodes();
        console.log(labels, 9999);
        let data = null;

        if (!labels) return this.$emit("getregionsname", null);
        if (this.multiple) {
          const obj_lists = [];
          // 多选，不能任意选取一个节点
          if (!this.checkStrictly) {
            value.forEach(res => {
              const _obj = labels.find(reg => (res.includes(reg.value) && !reg.hasChildren));
              if (_obj) obj_lists.push({ name: _obj.pathLabels, value: res });
            });
            /*
            *
            *  labels.forEach(reg => {
              // 查看父级能不能选省 并且父级已经全选了就不能选中城市
            if(reg.checked && reg.data.seletStatus === 1 && reg.level === 1){
              obj_lists.push(this.serializeData({ ...reg, value: [reg.value] }));
              return  false;
            }
            // 父级能选省 子级就不了
            if(reg.parent && reg.parent.data.seletStatus === 1 && reg.parent.checked){
              return  false;
            }
            if(!reg.hasChildren){
              const values = reg.parent ? [reg.parent.value, reg.value] : [reg.value];
              obj_lists.push(this.serializeData({ ...reg, value: values }));
            }
          });
            * */
          }
          console.log(obj_lists, "obj_lists");

          data = obj_lists;
        } else {
          // 单选
          data = { name: labels[0] && labels[0].pathLabels, value };
        }
        this.$emit("getregionsname", JSON.stringify(data), this.multiple);
      }
    },
    isEntireCountry(value) {
      return value.some((el) => (el === 0 || el.includes(0)));
    },
    /* 序列化数据*/
    serializeData(data) {
      return { name: data.pathLabels, value: data.value };
    }
  }
};
</script>

