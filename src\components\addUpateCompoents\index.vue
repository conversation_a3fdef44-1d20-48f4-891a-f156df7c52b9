<template>
  <!-- 弹窗 -->
  <app-popup
    footer-border
    :visible="dialogVisible"
    @close="cancel"
    v-bind="option"
    :show-close="false"
    :show-footer="false"
    show-cancel
    :width="option.width||'560px'"
    @cancel="cancel"
    @confirm="onSubmit('form')"
  >
    <slot name="beforeForm" />
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      :style="option.formStyle||{}"
      :label-width="(option && option.labelWidth) || '80px'"
      class="formClass"
    >
      <!-- item.controlItem['value']  值需要是字符串-->
      <div
        v-for="(item, key) in lists"
        :key="key">
        <el-form-item
          :label="item.type == 'inputOther' ? item['label']+`(${form[item.prop]})`: item['label']"
          :prop="item.prop"
          :class="item.className"
          v-if="((item.controlItem && item.controlItem['value'].includes(form[item.controlItem['name']])) || !item.controlItem) && !item.slotName"
        >

          <!-- 输入框 -->
          <el-input
            v-if="item.type == 'input'"
            :disabled="item['disabled'] || (detail && detail[item.prop] && item['editDisabled'] ? true :false)"
            v-model.trim="form[item.prop]"
            :type="item['typeName'] || 'text'"
            :placeholder="item.placeholder || ('请输入' + item['label'])"
            @input="inputValue(form[item.prop],item)"
          >
            <template #prepend v-if="item.prepend">{{item.prepend||''}}</template>
            <template #append v-if="item.append">{{item.append}}</template>
          </el-input>
          <!-- 输入框1 不同的字段绑定 -->
          <el-input
            v-if="item.type == 'inputOther'"
            :disabled="item['disabled'] || (detail && detail[item.prop] && item['editDisabled'] ? true :false)"
            v-model.trim="form[item.actProp]"
            :type="item['typeName'] || 'text'"
            :placeholder="item.placeholder || ('请输入' + item['label'])"
            @input="inputValue(form[item.actProp],item)"
          />

          <!-- 下拉选择框 -->
          <div v-if="item.type == 'select'">
            <el-select
              v-model="form[item.prop]"
              :filterable='true'
              :placeholder="item.placeholder || ('请选择' + item['label'])"
              :multiple='item["multiple"]'
              :multiple-limit='item["multipleLimit"]'
              :disabled="item['disabled'] || ((detail && !basics.isNull(detail[item.prop]) && item['editDisabled']) ? true :false)"
              @change="changeSelect(form[item.prop],item.syncData.data,item)"
            >
              <el-option
                v-for="(option, i) in item.syncData.data"
                :key="i"
                :label="!item.isSpliceData ? option[item.syncData.label || 'label'] : (option[item.syncData.label || 'label'] + '/'+ option[item.syncData.value])"
                :value="String(option[item.syncData.value || 'value'])"
              />
            </el-select>
          </div>
          <!-- :value=" (basics.isNumber(form[item.prop]) ?  Number(option[item.syncData.value || 'value'] ) :  option[item.syncData.value || 'value'] ) " -->
          <!-- textarea -->
          <div v-if="item.type == 'textarea'">
            <el-input
              type="textarea"
              v-model="form[item.prop]"
              :placeholder="item.placeholder"
              :maxlength="item['maxLength'] || null"
              :show-word-limit='item["showWordLimit"]'
              :disabled="item['disabled'] || (detail && detail[item.prop] && item['editDisabled'] ? true :false)"

            />
          </div>

          <!-- 单选框 -->
          <div v-if="item.type == 'radio'">
            <el-radio-group
              v-model="form[item.prop]"
              :disabled="item['disabled'] || (detail && detail[item.prop] && item['editDisabled'] ? true :false) "
            >
              <el-radio v-for="(option, i) in item.syncData.data" :key="i" :label="String(option[ 'value'])">
                {{option['label']}}
              </el-radio>
            </el-radio-group>
          </div>
          <!-- 可取消单选框 -->
          <div v-if="item.type == 'radiocancel'">
            <el-radio v-model="form[item.prop]" v-for="(option, i) in item.syncData.data" :key="i" :label="String(option[ 'value'])" @click.native.prevent="clickRadio(option,item)">{{
                option['label']
              }}</el-radio>
          </div>

          <!-- 多选框 -->
          <div v-if="item.type == 'checkbox'">
            <el-checkbox-group
              v-model="form[item.prop]"
              :disabled="item['disabled'] || (detail && detail[item.prop] && item['editDisabled'] ? true :false)"
            >
              <el-checkbox
                v-for="(option, i) in item.syncData.data"
                :name="item['prop']"
                :key="i"
                :label="String(option[item.syncData.value || 'value'])"
              >{{option[item.syncData.label || 'label']}}</el-checkbox>
            </el-checkbox-group>
          </div>

          <!-- 上传图片 -->
          <div v-if="item.type == 'upload'">
            <uploadImage
              :limit="item.limit"
              v-model="form[item.prop]"
              :disabled="item['disabled'] || (detail && detail[item.prop] && item['editDisabled'] ? true :false) "
              :accept="item.accept"
              :form-max-size-m="item['formMaxSizeM']"
              :file-data="item.fileData||{}"
            />
          </div>

          <!-- 上传文件 -->
          <div v-if="item.type == 'uploadFile'">
            <uploadFile
              :limit="item.limit"
              v-model="form[item.prop]"
              :action-url="item.url"
              :form-max-size-m="item['formMaxSizeM']"
              :accept="item.accept"
            />
          </div>

          <!-- 级联选择器 -->
          <div v-if="item.type == 'cascader'">
            <el-cascader
              v-model="form[item.prop]"
              :options="item.syncData.data"
              :props="item['props']"
              :disabled="item['disabled']"
              :clearable='item["clearable"]'
              :filterable='item["filterable"]'
              @change="handleChange(form[item.prop],item.prop,item['getLast'])"
            />
          </div>

          <!-- 开关 -->
          <div v-if="item.type == 'switch'">
            <el-switch
              v-model="form[item.prop]"
              :disabled="item['disabled']" />
          </div>

          <!-- 单个日期 -->
          <div v-if="item.type == 'date'">
            <el-date-picker
              v-model="form[item.prop]"
              :type="item['itemType']"
              placeholder="选择日期"
              :value-format="item.valueFormat || 'yyyyMMdd'"
              :picker-options="item['pickerOptions']" />
          </div>

          <!-- 地区  -->
          <div v-if="item.type == 'regioncascader'">
            <region
              v-model="form[item.prop]"
              :clearable='true'
              :check-strictly='item["checkStrictly"]'
              :get-names='true'
              :entire-country="item['entireCountry']"
              :multiple='item["multiple"]||false'
              @getregionsname='getRegionsName($event,item)' />
          </div>

          <!-- 金额 -->
          <div v-if="item.type == 'money'">
            <money-ipt
              v-model="form[item.prop]"
              :id-name='item.id'
              :placeholder='item.placeholder || "请输入"+item["label"]'
              :maxlength='item.maxlength'
              :keep-decimal='item.KeepDecimal'
              @input="inputValue(form[item.prop],item)"
              style="width: 90%"
            />
          </div>

        </el-form-item>
        <!-- 插入自定义的表单值 -->
        <slot :name="item.slotName" :form="form" />
      </div>

      <slot name="afterForm" />
    </el-form>
    <template #footerBtn>
      <slot name="footerBtn" />
    </template>
  </app-popup>
</template>

<script>
import uploadImage from "@/components/uploadImage/index.vue";
import uploadFile from "@/components/uploadFile/index.vue";
import Region from "@/components/regionCascader/index.vue";
import MoneyIpt from "@/components/inputMoney/index.vue";
import { copyObj } from "@/utils/toolMethods";
import AppPopup from "components/appPopup/index.vue";

export default {
  name: "AddContent",
  components: {
    AppPopup,
    uploadFile,
    uploadImage,
    Region,
    MoneyIpt,
  },
  props: {
    formALlDisabled: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array,
      default: () => []
    },
    dialogVisible: {
      type: Boolean,
      default: true
    },
    formDetail: {
      type: Object,
      default: () => {
        return {};
      }
    },
    option: {
      type: Object,
      default: () => {
        return {};
      }
    },
    addUpateUrl: {
      type: Object,
      default: () => {
        return {
          updateUrl: () => Promise.resolve(),
          getDetailUrl: () => Promise.resolve()
        };
      }
    },
    queryDetail: {
      type: Object,
      default: () => {}
    },
  },
  data() {
    return {
      form: {},
      lists: [], // 列表数据
      rules: {}, // 校验规则

      formTep: {},
      moneyFiled: [],

      loading: false
    };
  },
  computed: {
    // 传过来的详情数据处理
    detail() {
      if (this.formDetail) {
        console.log("我在执行");
        this.getDetail(this.formDetail);
      } else this.getAdd();

      return this.formDetail || {};
    }
  },
  created() {
    this.getData();
  },
  methods: {
    // 单选点击选项可取消
    clickRadio(cItem, item) {
      if (this.form[item.prop] === (cItem.value)) {
        this.form[item.prop] = "";
      } else {
        this.form[item.prop] = (cItem.value);
      }
    },
    // 新增的时候,置空，清除校验结果
    getAdd() {
      // for (const key in this.form) {
      //
      //   if (this.basics.isArray(this.form[key])) {
      //     this.$set(this.form, key, [])
      //   } else {
      //     this.$set(this.form, key, '')
      //   }
      // }
      // setTimeout(() => {
      //   if (this.$refs['form']) this.$refs['form'].clearValidate()
      // }, 50)
      // console.log('新增', this.form)
    },
    // 是否父组件传入方法
    isFunExist(val) {
      const isExistFun = val && ((val).toString().indexOf("Promise.resolve") === -1);

      return isExistFun;
    },
    //   获取详情，form
    getDetail() {
      const isExistFun = this.isFunExist(this.addUpateUrl.getDetailUrl);
      if (this.formDetail && !isExistFun) this.handleEnterData(this.formDetail);
      if (!isExistFun) return;

      this.addUpateUrl.getDetailUrl(this.queryDetail).then(data => {
        if (!data || this.basics.isObjNull(data)) return;
        this.handleEnterData(data);
      });
    },
    // 处理传入的数据
    handleEnterData(data) {
      const _keys = Object.keys(this.form);

      _keys.forEach(reg => {
        this.form[reg] = this.basics.isNumber(data[reg]) ? String(data[reg]) : data[reg];
      });
      const location = ["provinceCode", "regionCode", "provinceName", "regionName"];
      location.map(res => {
        if (data[res]) this.form[res] = data[res];
      });
      // 当编辑的时候，有id的时候，就不需要传id了
      if (data["id"]) this.form["id"] = data["id"];
      if (this.moneyFiled.length > 0) {
        this.moneyFiled.forEach(r => {
          this.formTep[r] = data[r] * 100;
        });
      }
      console.log(data, this.form, 1111, 999);
    },

    /**
     * @description: 数据的过滤 ,form表单生成
     * @param {*}
     * @return {*}
     */
    getData() {
      this.lists = copyObj(this.tableData).filter(res => !res.formHidden);

      this.lists.forEach(reg => {
        if (reg.formHidden) return;
        if (reg.prop) {
          this.$set(
            this.form,
            reg.prop,
            this.basics.isNumber(reg.defaultValue) ? String(reg.defaultValue) : reg.defaultValue
          );
        }
        // propKey存在，额外的字段
        if (reg.propKey) {
          reg.propKey.map(res => {
            this.$set(
              this.form,
              res.filed,
              ""
            );
          });
        }
        //   syncData的url获取值
        if (reg.syncData && reg.syncData.url) {
          if (!reg.syncData.data) {
            this.getUrlList(reg.syncData).then(options => {
              this.$set(reg.syncData, "data", options);
            });
            if (reg.syncData.prop && this.queryDetail) {
              this.$set(this.form, reg.prop, reg.syncData.prop);
            }
          }
        }

        //   验证规则,当编辑的时候，又不需要填写，就不需要验证
        if (reg.requireRule) {
          const obj = {};
          obj[reg.prop] = reg.requireRule;
          this.rules = Object.assign({}, this.rules, obj);
        }

        // 金额,编辑的时候，不更改给这里自动更改
        if (reg.isMoney || reg.type === "money") {
          this.moneyFiled.push(reg.prop);
        }
      });

      // this.getDetail()
    },
    // 处理数组的,加入label和value
    get(tree = [], lab, val) {
      const self = this;
      const arr = [];
      const label = lab;
      const value = val;
      if (!!tree && tree.length !== 0) {
        tree.forEach(item => {
          if (item) {
            let obj = {};
            obj = item;
            obj.label = obj[label || "columnName"];
            obj.value = String(obj[value || "id"]);
            if (item.children && item.children.length > 0) obj.children = self.get(item.children); // 递归调用
            arr.push(obj);
          }
        });
      }
      return arr;
    },
    // 获取syncData获取的值
    async getUrlList(item) {
      try {
        let data = [];
        await item.url(item.params).then(res => {
          data = res;
        });
        return this.get(data, item.label || "label", item.value || "value");
      } catch {
        //  return re
      }
    },
    //   取消
    cancel() {
      this.$emit("update:dialog-visible", false);
    },
    // 输入框
    inputValue(val, { prop, isMoney, type }) {
      if ((isMoney || type === "money")) {
        if(this.basics.isNull(val)){
          this.formTep[prop] = "";
        }else {
          // 金额转成分
          this.formTep[prop] = val * 100;
        }
      }
    },
    // select的下拉框的change事件
    /**
     * @param
     *  val:选中的值、
     *  lists:循环的数据、
     *  propKey：需要赋值的字段，格式为[{ name:'label',filed:'typeLabel' }]
     */

    changeSelect(val, lists, { propKey, multiple, prop, multipleMoreName, multipleNoString, resetKey }) {
      // 关联选择处理 重置清空
      if (resetKey) {
        (resetKey || []).forEach(key => {
          this.$set(this.form, key, "");
        });
      }
      if (multipleNoString) return;
      if (multiple) { this.formTep[prop] = val.toString(); }

      if (!propKey) return;
      const tempLits = [];

      const obj = this.basics.isArray(val) ? lists.filter(res => val.find(reg => res["value"] === reg)) : lists.find(res => res["value"] === val);

      if (this.basics.isArray(obj)) {
        obj.forEach(reg => {
          const temp = {};
          propKey.forEach(rep => {
            temp[rep["filed"]] = reg[rep["name"]];
          });
          tempLits.push(temp);
        });
        return this.$set(this.formTep, multipleMoreName, tempLits);
      }
      propKey.map(res => {
        if (this.basics.isArray(obj)) {
          const temp = {};
          obj.map(reg => {
            temp[res["filed"]] = reg[res["name"]];
          });
        } else {
          this.formTep[res["filed"]] = obj[res["name"]];
        }
      });
    },
    //   级联选择器,是否取最后一级
    /**
     * @param
     *  value:选中的值、
     *  filed:表单的哪个字段、
     *  isGetLast：是否取最后一个值
     */
    handleChange(value, filed, isGetLast) {
      if (isGetLast) {
        this.formTep[filed] = value[value.length - 1];
        // this.form[filed] = value[value.length - 1]
      }
    },
    // 地区选择,地区的名字,code
    getRegionsName(val, item) {
      const { value, name } = JSON.parse(val);
      const options = item.options;
      if (options && options.provinceName) {
        this.form[options.provinceCode] = value && value[0];
        this.form[options.regionCode] = value && value[1];
        this.form[options.provinceName] = name && name[0];
        this.form[options.regionName] = name && name[1];
      } else {
        this.form["provinceCode"] = value && value[0];
        this.form["regionCode"] = value && value[1];
        this.form["provinceName"] = name && name[0];
        this.form["regionName"] = name && name[1];
      }
    },
    // 日期处理
    handleTimerChange(val, item) {
      console.log(val, item);
      if (item.typeTime === "minute") { this.formTep[item.prop] = val + ":00"; }
    },
    //   确定
    onSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          const _data = Object.assign({}, this.form, this.queryDetail, this.formTep);
          console.log(11111, _data, this.queryDetail);

          // 不存在传入更新的方法的时候
          const isExistFun = this.isFunExist(this.addUpateUrl.updateUrl);
          if (!isExistFun) return this.$emit("sure", _data);
          this.addUpateUrl.updateUrl(_data).then(res => {
            this.$message.success("操作成功");
            // 刷新表格
            this.$store.dispatch("setIsTableRefresh");
            this.$emit("sure", _data);

            this.cancel();
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }

  }
};
</script>

<style lang="scss" scoped>

.footer-btn {
  position: absolute;
  bottom: 0;
  right: 15px;
}

.formClass {
  margin-bottom: 16px;
  padding-left: 24px;
  margin-top: 16px;
  box-sizing: border-box;

  .el-select,
  .el-input,
  .el-cascader,
  .el-textarea,
  .el-input-number {
    width: 90%;
  }
  .el-textarea /deep/ textarea {
    height: 150px;
    resize: none;
  }
}
.re-size {
  .el-textarea /deep/ textarea {
    resize: auto !important;
  }
}

.radios-wrap /deep/ .el-radio{
  width: 100%;
  line-height: 25px;
}

.title{
  font-weight: bold;
}
</style>
