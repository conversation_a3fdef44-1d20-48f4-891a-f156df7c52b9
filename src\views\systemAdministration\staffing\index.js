import global from "@/utils/constant";
import { certStatusState, cityRangeState, staffingState } from "@/enum/staffing.js";
import { regular } from "utils/validate.js";
import { lcInstitutionStaffGetStaffPhoneById } from "@/api/system.js";
import { getAreaByInstitutionId } from "@/api/common.js";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";

console.log(global, 91);

export const column = [
  {
    label: "员工姓名",
    prop: "userName",
    type: global.formItemType.input,
    search: true,
    minWidth: "90px",
    level: 2,
    requireRule: [{ required: true, message: "请输入员工姓名", trigger: "blur" }],
  },
  {
    propKey: ["createTimeStart", "createTimeEnd"],
    label: "创建时间",
    prop: "updateUserName11",
    tableHidden: true,
    formHidden: true,
    type: global.formItemType.daterange,
    valueFormat: "yyyy-MM-dd",
    search: true,
  },
  {
    label: "可见城市范围",
    prop: "workCityType",
    minWidth: "90px",
    type: global.formItemType.radio,
    defaultValue: 0,
    level: 4,
    tableHidden: true,
    requireRule: [{ required: true, message: "请选择可见城市范围", trigger: "blur" }],
    syncData: {
      data: cityRangeState
    },
  },
  {
    label: "",
    prop: "workCityList",
    minWidth: "90px",
    type: global.formItemType.regioncascader,
    multiple: true,
    checkStrictly: true,
    cityApi: getAreaByInstitutionId,
    level: 4,
    tableHidden: true,
    requireRule: [{ required: true, message: "请选择可见城市范围", trigger: "blur" }],
    controlItem: {
      name: "workCityType",
      Symbol: "===",
      value: ["1"]
    },
  },
  {
    label: "岗位名称",
    prop: "jobTitle",
    minWidth: "90px",
    type: global.formItemType.input,
    level: 4,
    requireRule: [{ required: true, message: "请输入岗位名称", trigger: "blur" }],
  },
  {
    label: "登录手机号",
    prop: "userPhone",
    search: true,
    type: global.formItemType.input,
    maxLength: 11,
    level: 3,
    minWidth: "200px",
    requireRule: [
      { required: true, message: "请输入登录手机号", trigger: "blur" },
      { validator: regular("phone"), trigger: "blur" },
    ],
    renderComponentName: "LookPhone",
    lookRequest: lcInstitutionStaffGetStaffPhoneById,
  },
  {
    label: "最新登录律客云时间",
    prop: "lastLoginTime",
    minWidth: "180px",
    formHidden: true,
  },
  {
    label: "账号状态",
    prop: "status",
    minWidth: "100px",
    search: true,
    formHidden: true,
    type: global.formItemType.select,
    filter: staffingState,
    syncData: {
      data: staffingState,
    },
    filterItemStyleRule: {
      1: { color: "#666666" },
      0: { color: "#EB4738" },
    },
  },
  {
    label: "实名状态",
    prop: "certStatus",
    minWidth: "100px",
    formHidden: true,
    type: global.formItemType.select,
    filter: certStatusState,
    filterItemStyleRule: {
      1: { color: "#22BF7E" },
      0: { color: "#EB4738" },
    },
  },
  ...isLeadPackageDisplay([
    {
      label: "案源每日可抢权益数",
      prop: "caseSourceDayLimit",
      type: global.formItemType.input,
      maxLength: 8,
      minWidth: "160px",
      requireRule: [
        { validator: regular("number"), trigger: "blur" },
      ],
    },
    {
      label: "案源上限可抢权益数",
      prop: "caseSourceAccumulateLimit",
      type: global.formItemType.input,
      minWidth: "165px",
      maxLength: 8,
      requireRule: [
        { validator: regular("number"), trigger: "blur" }
      ],
    },
    {
      label: "即时咨询每日可抢权益数",
      prop: "qaMessageDayLimit",
      type: global.formItemType.input,
      maxLength: 8,
      minWidth: "190px",
      requireRule: [
        { validator: regular("number"), trigger: "blur" },
      ],
    },
    {
      label: "即时咨询上限可抢权益数",
      prop: "qaMessageAccumulateLimit",
      type: global.formItemType.input,
      maxLength: 8,
      minWidth: "190px",
      requireRule: [
        { validator: regular("number"), trigger: "blur" }
      ],
    }], []),
  {
    label: "案源每日使用法临币数(个)",
    prop: "caseSourceFlbDayLimit",
    type: "flbTransformed",
    formHidden: true,
    minWidth: "230px",
  },
  {
    label: "案源上限使用法临币数(个)",
    prop: "caseSourceFlbAccumulateLimit",
    type: "flbTransformed",
    formHidden: true,
    minWidth: "230px",
  },
  {
    label: "即时咨询每日使用法临币数(个)",
    prop: "qaMessageFlbDayLimit",
    type: "flbTransformed",
    formHidden: true,
    minWidth: "230px",
  },
  {
    label: "即时咨询上限使用法临币数(个)",
    prop: "qaMessageFlbAccumulateLimit",
    type: "flbTransformed",
    formHidden: true,
    minWidth: "230px",
  },
  {
    label: "案源每日使用法临币数(个)",
    prop: "caseSourceFlbDayLimit",
    type: global.formItemType.input,
    tableHidden: true,
    isMoney: true,
    maxLength: 6,
    requireRule: [
      { validator: regular("number"), trigger: "blur" },
    ],
  },
  {
    label: "案源上限使用法临币数(个)",
    prop: "caseSourceFlbAccumulateLimit",
    type: global.formItemType.input,
    tableHidden: true,
    isMoney: true,
    maxLength: 6,
    requireRule: [
      { validator: regular("number"), trigger: "blur" }
    ],
  },
  {
    label: "即时咨询每日使用法临币数(个)",
    prop: "qaMessageFlbDayLimit",
    type: global.formItemType.input,
    tableHidden: true,
    isMoney: true,
    maxLength: 6,
    requireRule: [
      { validator: regular("number"), trigger: "blur" },
    ],
  },
  {
    label: "即时咨询上限使用法临币数(个)",
    prop: "qaMessageFlbAccumulateLimit",
    type: global.formItemType.input,
    tableHidden: true,
    isMoney: true,
    maxLength: 6,
    requireRule: [
      { validator: regular("number"), trigger: "blur" }
    ],
  }
];
